// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/profile/domain/entities/my_ticket_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'my_ticket_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class MyTicketModel extends MyTicketEntity {
  const MyTicketModel({
    super.ticket_id,
    super.sector,
    super.row,
    super.seat,
    super.match,
    super.start_date,
    super.price,
  });

  factory MyTicketModel.fromJson(Map<String, dynamic> json) =>
      _$MyTicketModelFromJson(json);
}
